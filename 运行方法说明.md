# 零信任SAGIN多Agent威胁分析系统 - 运行方法说明

## 📋 项目概述

本项目是一个基于多Agent协作的零信任SAGIN（Space-Air-Ground Integrated Network）威胁分析系统，实现了四个核心Agent的协作工作流程：

- **Summarization Agent**: 威胁数据摘要和分类
- **Prompt Agent**: 数据格式转换和提示生成  
- **Specific Advice Agent**: CVSS评分和安全建议
- **Comprehensive Decision Agent**: 综合决策和策略选择

## 🚀 快速开始

### 系统要求

**最低配置**
- Python 3.8+
- 8GB RAM (测试模式) / 16GB RAM (LLM模式)
- 5GB 可用存储空间

**推荐配置**
- Python 3.8+
- 32GB RAM
- NVIDIA GPU (LLM模式)
- SSD硬盘

### 环境安装

#### 1. 创建虚拟环境
```bash
# 使用conda (推荐)
conda create -n sagin-env python=3.8
conda activate sagin-env

# 或使用venv
python -m venv sagin-env
source sagin-env/bin/activate  # Linux/macOS
# sagin-env\Scripts\activate  # Windows
```

#### 2. 安装依赖

**基础依赖 (测试模式)**
```bash
pip install numpy pandas torch transformers
```

**完整依赖 (LLM模式)**
```bash
pip install torch transformers openai sentence-transformers faiss-cpu numpy pandas
```

**GPU支持 (可选)**
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install faiss-gpu
```

#### 3. 验证安装
```bash
python -c "import torch, transformers, numpy, pandas; print('安装成功')"
```

### 数据文件准备

确保以下数据文件存在于项目根目录：

```
项目根目录/
├── output_0515_dataset_fin.json          # 威胁数据集 (必需)
├── output_0525_finetune_metrics.json     # CVSS评分数据 (必需)  
├── output_1112_strategy_train_data.json  # 策略训练数据 (必需)
├── output_1105_dataset_additional.json   # 附加威胁数据 (可选)
└── output_1113_score_train_data.json     # 评分训练数据 (可选)
```

## 🎯 运行方式

### 方式1: 完整Demo运行 (推荐)

#### 测试模式运行 (推荐)
```bash
# 基础运行 - 无需模型文件，快速演示
python demo_complete_workflow.py --test-mode

# 详细日志模式
python demo_complete_workflow.py --test-mode --verbose

# 静默模式
python demo_complete_workflow.py --test-mode --quiet

# 完整工作流程
python demo_complete_workflow.py --test-mode --full-workflow
```

#### LLM模式运行 (需要模型文件)
```bash
# 使用本地Llama3模型
python demo_complete_workflow.py --model-path ./models/llama3-7b

# 启用4-bit量化 (节省内存)
python demo_complete_workflow.py --model-path ./models/llama3-7b --quantization

# 自定义生成参数
python demo_complete_workflow.py --model-path ./models/llama3-7b --temperature 0.8 --max-length 1024
```

### 方式2: 单独Agent测试

#### Summarization Agent (威胁分类)
```bash
# 基础威胁分类演示
python demo_task1.py

# 自定义测试
python test_summarization_agent.py
```

#### Prompt Agent (数据转换)
```bash
# 基础数据转换演示
python demo_prompt_agent.py

# 测试模式
python test_prompt_agent.py
```

#### Specific Advice Agent (CVSS评分)
```bash
# 基础CVSS评分演示
python demo_specific_advice_agent.py

# 简化测试
python demo_specific_advice_simple.py
```

#### Comprehensive Decision Agent (策略决策)
```bash
# 基础策略选择演示
python demo_comprehensive_decision_agent.py

# LLM集成测试
python demo_llm_strategy_selection_simple.py
```

### 方式3: 协作工作流运行

#### 简化协作流程
```bash
python simple_agent_collaboration.py
```

#### 完整协作流程
```bash
python demo_agent_collaboration.py
```

#### 工作流测试
```bash
python test_agent_collaboration_workflow.py
```

## ⚙️ 配置选项

### 命令行参数

#### 模式选择
- `--test-mode`: 使用测试模式 (推荐，无需模型文件)
- `--llm-mode`: 使用LLM模式 (需要模型文件)

#### 模型配置
- `--model-path MODEL_PATH`: 模型路径 (默认: ./models/llama3-7b)
- `--model-type MODEL_TYPE`: 模型类型 (默认: llama3-7b)
- `--quantization`: 启用4-bit量化
- `--device DEVICE`: 设备选择 (auto/cpu/cuda)

#### 生成参数
- `--max-length MAX_LENGTH`: 最大生成长度 (默认: 2048)
- `--temperature TEMPERATURE`: 生成温度 (默认: 0.7)
- `--top-p TOP_P`: Top-p采样 (默认: 0.9)

#### 输出选项
- `--verbose`: 详细日志
- `--quiet`: 静默模式
- `--full-workflow`: 完整工作流

### 配置文件

#### model_config.json
```json
{
  "model_type": "llama3-7b",
  "model_path": "./models/llama3-7b",
  "max_length": 2048,
  "temperature": 0.7,
  "top_p": 0.9,
  "quantization": true,
  "device": "auto",
  "test_mode": false
}
```

## 📊 输出结果

### 控制台输出示例
```
🔧 配置信息:
   模式: 测试模式
   模型: 规则引擎
   数据集: 3个文件

✅ 环境验证:
   Python依赖: 正常
   数据文件: 完整
   系统资源: 充足

📋 Agent执行结果:
   Summarization Agent: ✅ 处理128条威胁记录
   Prompt Agent: ✅ 生成7种威胁类型提示
   Specific Advice Agent: ✅ 生成CVSS评分和建议
   Comprehensive Decision Agent: ✅ 选择安全策略

📈 性能指标:
   总处理时间: 2.34秒
   威胁分类准确率: 92.5%
   CVSS评分一致性: 95.2%
   策略选择有效性: 88.7%
```

### 报告文件

详细的JSON格式报告保存在以下目录：
- `demo_outputs/`: 完整工作流报告
- `summarization_outputs/`: 威胁分类结果
- `prompt_agent_outputs/`: 数据转换结果
- `specific_advice_outputs/`: CVSS评分结果
- `comprehensive_decision_outputs/`: 策略决策结果

## 🔧 Agent协作流程

系统实现以下Agent协作路径：

```
原始威胁数据 → Summarization Agent → Prompt Agent → Specific Advice Agent → Comprehensive Decision Agent
```

### 各Agent功能详解

1. **Summarization Agent**: 
   - 威胁数据预处理和分类
   - 支持7种威胁类型识别
   - 输出结构化威胁摘要

2. **Prompt Agent**: 
   - 数据格式标准化转换
   - 生成下游Agent所需的提示
   - 协调Agent间数据流

3. **Specific Advice Agent**: 
   - 生成CVSS 3.1评分
   - 提供安全建议和风险评估
   - 基于历史案例的上下文分析

4. **Comprehensive Decision Agent**: 
   - 综合威胁分析结果
   - 选择最优安全策略组合
   - 生成行动计划和优先级

## 🐛 故障排除

### 常见问题

#### 1. 数据文件缺失
```
❌ 错误: 数据文件缺失: output_0515_dataset_fin.json
```
**解决方案**: 确保所有必需的数据文件存在于项目根目录

#### 2. Python依赖缺失
```
❌ 错误: ModuleNotFoundError: No module named 'transformers'
```
**解决方案**: 
```bash
pip install transformers torch numpy pandas
```

#### 3. 内存不足 (LLM模式)
```
❌ 错误: CUDA out of memory
```
**解决方案**: 
- 使用测试模式: `--test-mode`
- 启用量化: `--quantization`
- 使用CPU模式: `--device cpu`

#### 4. 模型路径不存在 (LLM模式)
```
❌ 错误: 模型路径不存在: ./models/llama3-7b
```
**解决方案**: 使用测试模式 `--test-mode` 或下载正确的模型文件

### 调试技巧

#### 启用详细日志
```bash
python demo_complete_workflow.py --test-mode --verbose
```

#### 单步调试
```bash
# 逐个测试Agent
python demo_task1.py
python demo_prompt_agent.py  
python demo_specific_advice_agent.py
python demo_comprehensive_decision_agent.py
```

#### 验证数据格式
```bash
python -c "import json; print(json.load(open('output_0515_dataset_fin.json'))[:2])"
```

## 📋 最佳实践

### 开发环境
1. 使用**测试模式**进行开发和调试
2. 启用**详细日志**便于问题定位
3. **单独测试Agent**验证功能正确性

### 生产环境  
1. 使用**LLM模式**获得最佳分析效果
2. 配置适当的**资源限制**避免系统过载
3. 设置**监控和告警**及时发现问题

## 📞 获取帮助

### 查看帮助信息
```bash
python demo_complete_workflow.py --help
```

### 运行系统测试
```bash
python test_task9_final_validation.py
```

### 技术文档
- `README_demo.md`: Demo运行指南
- `使用说明文档.md`: 详细使用说明
- `系统结构说明文档.md`: 系统架构说明
- `项目文档.md`: 项目功能文档

---

**文档版本**: v1.0.0  
**最后更新**: 2025-07-31  
**适用系统版本**: v1.0.0+
