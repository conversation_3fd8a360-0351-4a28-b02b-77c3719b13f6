# 零信任SAGIN多Agent威胁分析系统 - 精简版

## 📋 项目概述

这是零信任SAGIN多Agent威胁分析系统的精简版，包含了系统运行所需的核心文件，去除了测试文件、输出文件和其他无关文件，便于部署和分发。

## 📁 文件结构

```
agent_simplified/
├── 📄 README.md                           # 本文档
├── 📄 运行方法说明.md                      # 详细运行方法说明
├── 📄 README_demo.md                       # Demo运行指南
├── 📄 使用说明文档.md                      # 完整使用说明
├── 📄 系统结构说明文档.md                  # 系统架构说明
├── 📄 项目文档.md                          # 项目功能文档
│
├── 🤖 核心Agent文件/
│   ├── Comprehensive Decision Agent.py    # 综合决策Agent
│   ├── Prompt Agent.py                    # 提示生成Agent
│   ├── Specific Advice Agent.py           # 特定建议Agent
│   └── Summarization Agent.py             # 信息摘要Agent
│
├── ⚙️ 配置和工作流/
│   ├── model_config.py                    # 统一模型配置
│   ├── data_loader.py                     # 数据加载器
│   ├── agent_collaboration_workflow.py    # 完整协作工作流
│   ├── simple_agent_collaboration.py      # 简化协作工作流
│   └── demo_complete_workflow.py          # 完整Demo运行脚本
│
├── 🎯 演示脚本/
│   ├── demo_task1.py                      # Summarization Agent演示
│   ├── demo_prompt_agent.py               # Prompt Agent演示
│   ├── demo_specific_advice_agent.py      # Specific Advice Agent演示
│   ├── demo_comprehensive_decision_agent.py # Comprehensive Decision Agent演示
│   └── demo_agent_collaboration.py        # Agent协作演示
│
├── 📊 数据文件/
│   ├── output_0515_dataset_fin.json       # 威胁数据集 (必需)
│   ├── output_0525_finetune_metrics.json  # CVSS评分数据 (必需)
│   └── output_1112_strategy_train_data.json # 策略训练数据 (必需)
│
└── 🔧 配置文件/
    ├── model_config.json                  # 模型配置
    └── custom_model_config.json           # 自定义配置
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
conda create -n sagin-env python=3.8
conda activate sagin-env

# 安装基础依赖 (测试模式)
pip install numpy pandas torch transformers

# 安装完整依赖 (LLM模式)
pip install torch transformers openai sentence-transformers faiss-cpu numpy pandas
```

### 2. 运行系统

#### 推荐方式：测试模式运行
```bash
# 基础运行 - 无需模型文件
python demo_complete_workflow.py --test-mode

# 详细日志模式
python demo_complete_workflow.py --test-mode --verbose

# 完整工作流程
python demo_complete_workflow.py --test-mode --full-workflow
```

#### LLM模式运行 (需要模型文件)
```bash
# 使用本地Llama3模型
python demo_complete_workflow.py --model-path ./models/llama3-7b

# 启用量化节省内存
python demo_complete_workflow.py --model-path ./models/llama3-7b --quantization
```

### 3. 单独Agent测试

```bash
# 威胁分类演示
python demo_task1.py

# 数据转换演示  
python demo_prompt_agent.py

# CVSS评分演示
python demo_specific_advice_agent.py

# 策略决策演示
python demo_comprehensive_decision_agent.py

# Agent协作演示
python demo_agent_collaboration.py
```

## 🎯 系统功能

### Agent协作流程
```
原始威胁数据 → Summarization Agent → Prompt Agent → Specific Advice Agent → Comprehensive Decision Agent
```

### 各Agent功能
1. **Summarization Agent**: 威胁数据预处理和分类
2. **Prompt Agent**: 数据格式标准化转换
3. **Specific Advice Agent**: CVSS 3.1评分和安全建议
4. **Comprehensive Decision Agent**: 综合决策和策略选择

## 📊 输出结果

### 控制台输出
- 配置信息和环境验证
- 各Agent执行结果和状态
- 性能指标和统计数据
- 执行摘要和建议

### 报告文件
系统会自动创建以下输出目录并保存详细报告：
- `demo_outputs/`: 完整工作流报告
- `summarization_outputs/`: 威胁分类结果
- `prompt_agent_outputs/`: 数据转换结果
- `specific_advice_outputs/`: CVSS评分结果
- `comprehensive_decision_outputs/`: 策略决策结果

## ⚙️ 配置选项

### 命令行参数
- `--test-mode`: 测试模式 (推荐，无需模型文件)
- `--model-path PATH`: 模型路径
- `--quantization`: 启用4-bit量化
- `--verbose`: 详细日志
- `--quiet`: 静默模式
- `--full-workflow`: 完整工作流

### 配置文件
- `model_config.json`: 模型配置
- `custom_model_config.json`: 自定义配置

## 🔧 故障排除

### 常见问题
1. **数据文件缺失**: 确保三个必需的JSON数据文件存在
2. **依赖缺失**: 运行 `pip install numpy pandas torch transformers`
3. **内存不足**: 使用 `--test-mode` 或 `--quantization`
4. **模型路径错误**: 使用 `--test-mode` 避免模型依赖

### 调试技巧
```bash
# 启用详细日志
python demo_complete_workflow.py --test-mode --verbose

# 单步调试
python demo_task1.py
python demo_prompt_agent.py

# 验证数据格式
python -c "import json; print(json.load(open('output_0515_dataset_fin.json'))[:2])"
```

## 📋 与完整版的区别

### 精简版包含
✅ 核心Agent文件  
✅ 主要配置和工作流文件  
✅ 演示脚本  
✅ 必需的数据文件  
✅ 完整文档  

### 精简版移除
❌ 测试文件 (`test_*.py`)  
❌ 输出目录 (`*_outputs/`, `__pycache__/`)  
❌ 临时文件和日志文件  
❌ 可选的数据文件  
❌ 验证和调试脚本  

## 📞 获取帮助

### 查看帮助
```bash
python demo_complete_workflow.py --help
```

### 文档参考
- `运行方法说明.md`: 详细运行方法
- `使用说明文档.md`: 完整使用指南
- `系统结构说明文档.md`: 系统架构
- `项目文档.md`: 功能说明

## 🎉 开始使用

推荐从测试模式开始：

```bash
python demo_complete_workflow.py --test-mode --verbose
```

这将运行完整的Agent协作流程，展示系统的所有功能，无需任何模型文件或复杂配置。

---

**精简版版本**: v1.0.0  
**基于完整版**: v1.0.0  
**创建日期**: 2025-07-31
